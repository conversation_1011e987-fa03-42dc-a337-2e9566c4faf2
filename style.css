
:root {
  /* Colors */
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);
  --color-select-caret: rgba(19, 52, 59, 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: rgba(31, 33, 33, 1);
    --color-surface: rgba(38, 40, 40, 1);
    --color-text: rgba(245, 245, 245, 1);
    --color-text-secondary: rgba(167, 169, 169, 0.7);
    --color-primary: rgba(50, 184, 198, 1);
    --color-primary-hover: rgba(45, 166, 178, 1);
    --color-primary-active: rgba(41, 150, 161, 1);
    --color-secondary: rgba(119, 124, 124, 0.15);
    --color-secondary-hover: rgba(119, 124, 124, 0.25);
    --color-secondary-active: rgba(119, 124, 124, 0.3);
    --color-border: rgba(119, 124, 124, 0.3);
    --color-error: rgba(255, 84, 89, 1);
    --color-success: rgba(50, 184, 198, 1);
    --color-warning: rgba(230, 129, 97, 1);
    --color-info: rgba(167, 169, 169, 1);
    --color-focus-ring: rgba(50, 184, 198, 0.4);
    --color-btn-primary-text: rgba(19, 52, 59, 1);
    --color-card-border: rgba(119, 124, 124, 0.2);
    --color-card-border-inner: rgba(119, 124, 124, 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(119, 124, 124, 0.2);
    --color-border-secondary: rgba(119, 124, 124, 0.2);
    --color-select-caret: rgba(245, 245, 245, 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: 50, 184, 198;
    --color-error-rgb: 255, 84, 89;
    --color-warning-rgb: 230, 129, 97;
    --color-info-rgb: 167, 169, 169;
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  --color-background: rgba(31, 33, 33, 1);
  --color-surface: rgba(38, 40, 40, 1);
  --color-text: rgba(245, 245, 245, 1);
  --color-text-secondary: rgba(167, 169, 169, 0.7);
  --color-primary: rgba(50, 184, 198, 1);
  --color-primary-hover: rgba(45, 166, 178, 1);
  --color-primary-active: rgba(41, 150, 161, 1);
  --color-secondary: rgba(119, 124, 124, 0.15);
  --color-secondary-hover: rgba(119, 124, 124, 0.25);
  --color-secondary-active: rgba(119, 124, 124, 0.3);
  --color-border: rgba(119, 124, 124, 0.3);
  --color-error: rgba(255, 84, 89, 1);
  --color-success: rgba(50, 184, 198, 1);
  --color-warning: rgba(230, 129, 97, 1);
  --color-info: rgba(167, 169, 169, 1);
  --color-focus-ring: rgba(50, 184, 198, 0.4);
  --color-btn-primary-text: rgba(19, 52, 59, 1);
  --color-card-border: rgba(119, 124, 124, 0.15);
  --color-card-border-inner: rgba(119, 124, 124, 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(119, 124, 124, 0.2);
  --color-select-caret: rgba(245, 245, 245, 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: 50, 184, 198;
  --color-error-rgb: 255, 84, 89;
  --color-warning-rgb: 230, 129, 97;
  --color-info-rgb: 167, 169, 169;
}

[data-color-scheme="light"] {
  --color-background: rgba(252, 252, 249, 1);
  --color-surface: rgba(255, 255, 253, 1);
  --color-text: rgba(19, 52, 59, 1);
  --color-text-secondary: rgba(98, 108, 113, 1);
  --color-primary: rgba(33, 128, 141, 1);
  --color-primary-hover: rgba(29, 116, 128, 1);
  --color-primary-active: rgba(26, 104, 115, 1);
  --color-secondary: rgba(94, 82, 64, 0.12);
  --color-secondary-hover: rgba(94, 82, 64, 0.2);
  --color-secondary-active: rgba(94, 82, 64, 0.25);
  --color-border: rgba(94, 82, 64, 0.2);
  --color-btn-primary-text: rgba(252, 252, 249, 1);
  --color-card-border: rgba(94, 82, 64, 0.12);
  --color-card-border-inner: rgba(94, 82, 64, 0.12);
  --color-error: rgba(192, 21, 47, 1);
  --color-success: rgba(33, 128, 141, 1);
  --color-warning: rgba(168, 75, 47, 1);
  --color-info: rgba(98, 108, 113, 1);
  --color-focus-ring: rgba(33, 128, 141, 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* Enhanced styles for DataFlow Pro Analytics Platform */

/* Layout Structure */
body {
    display: flex;
    min-height: 100vh;
    margin: 0;
    font-family: var(--font-family-base);
    background-color: var(--color-background);
    color: var(--color-text);
}

/* Sidebar Navigation */
.sidebar {
    width: 280px;
    background-color: var(--color-surface);
    border-right: 1px solid var(--color-border);
    transition: transform var(--duration-normal) var(--ease-standard);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.sidebar-header {
    padding: var(--space-24) var(--space-20);
    border-bottom: 1px solid var(--color-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h2 {
    margin: 0;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    cursor: pointer;
    color: var(--color-text);
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: var(--space-12) var(--space-20);
    cursor: pointer;
    transition: all var(--duration-fast) var(--ease-standard);
    border-left: 3px solid transparent;
}

.nav-item:hover {
    background-color: var(--color-secondary);
}

.nav-item.active {
    background-color: var(--color-secondary);
    border-left-color: var(--color-primary);
}

.nav-icon {
    margin-right: var(--space-12);
    font-size: var(--font-size-lg);
}

.nav-text {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-base);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header */
.header {
    background-color: var(--color-surface);
    border-bottom: 1px solid var(--color-border);
    padding: var(--space-20) var(--space-32);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left h1 {
    margin: 0;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
}

.header-left p {
    margin: var(--space-4) 0 0 0;
    color: var(--color-text-secondary);
    font-size: var(--font-size-base);
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--space-16);
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid var(--color-border);
}

/* Content Sections */
.content-section {
    display: none;
    padding: var(--space-32);
    flex: 1;
}

.content-section.active {
    display: block;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-24);
    margin-bottom: var(--space-32);
}

.metric-card {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
    display: flex;
    align-items: center;
    gap: var(--space-16);
    transition: all var(--duration-normal) var(--ease-standard);
}

.metric-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.metric-icon {
    font-size: 2.5rem;
    background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.metric-content h3 {
    margin: 0 0 var(--space-8) 0;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    margin: 0;
    color: var(--color-text);
}

.metric-change {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    margin-top: var(--space-8);
    display: inline-block;
}

.metric-change.positive {
    background-color: rgba(var(--color-success-rgb), 0.1);
    color: var(--color-success);
}

.metric-change.negative {
    background-color: rgba(var(--color-error-rgb), 0.1);
    color: var(--color-error);
}

/* AI Insights Panel */
.ai-insights-panel {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-32);
    margin-bottom: var(--space-32);
}

.ai-insights-panel h2 {
    margin: 0 0 var(--space-24) 0;
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-20);
}

.insight-card {
    background: var(--color-background);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-md);
    padding: var(--space-20);
    position: relative;
    overflow: hidden;
}

.insight-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--color-info);
}

.insight-card.critical::before {
    background: var(--color-error);
}

.insight-card.high::before {
    background: var(--color-warning);
}

.insight-card.medium::before {
    background: var(--color-primary);
}

.insight-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-12);
}

.insight-type {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    color: var(--color-text-secondary);
}

.confidence-score {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-primary);
    background: rgba(var(--color-success-rgb), 0.1);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
}

.insight-card p {
    margin: 0 0 var(--space-16) 0;
    line-height: 1.6;
    color: var(--color-text);
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-32);
    margin-bottom: var(--space-32);
}

.chart-container {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-20);
}

.chart-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.chart-controls {
    display: flex;
    gap: var(--space-8);
}

.chart-wrapper {
    position: relative;
    height: 300px;
}

.chart-wrapper canvas {
    width: 100% !important;
    height: 100% !important;
}

/* Predictive Analytics */
.predictive-header {
    margin-bottom: var(--space-32);
}

.predictive-header h2 {
    margin: 0 0 var(--space-8) 0;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
}

.predictive-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-24);
}

.prediction-card {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
    transition: all var(--duration-normal) var(--ease-standard);
}

.prediction-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.prediction-card h3 {
    margin: 0 0 var(--space-16) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.prediction-visual {
    margin-bottom: var(--space-16);
    padding: var(--space-16);
    background: var(--color-background);
    border-radius: var(--radius-md);
}

.trend-line {
    height: 60px;
    border-radius: var(--radius-sm);
    margin-bottom: var(--space-12);
    position: relative;
    overflow: hidden;
}

.trend-line.up {
    background: linear-gradient(45deg, rgba(var(--color-success-rgb), 0.1), rgba(var(--color-success-rgb), 0.3));
}

.trend-line.down {
    background: linear-gradient(45deg, rgba(var(--color-error-rgb), 0.1), rgba(var(--color-error-rgb), 0.3));
}

.trend-line.stable {
    background: linear-gradient(45deg, rgba(var(--color-primary-rgb), 0.1), rgba(var(--color-primary-rgb), 0.3));
}

.prediction-text {
    margin: 0;
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.prediction-metrics {
    display: flex;
    gap: var(--space-16);
}

.prediction-metrics .metric {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-text-secondary);
    background: var(--color-secondary);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
}

/* Analytics Builder */
.builder-workspace {
    display: grid;
    grid-template-columns: 250px 1fr 250px;
    gap: var(--space-24);
    height: 600px;
}

.component-palette, .properties-panel {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-20);
}

.component-palette h3, .properties-panel h3 {
    margin: 0 0 var(--space-16) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.palette-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-8);
}

.component-item {
    padding: var(--space-12);
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    cursor: grab;
    transition: all var(--duration-fast) var(--ease-standard);
    font-size: var(--font-size-sm);
}

.component-item:hover {
    background: var(--color-secondary);
    transform: translateX(4px);
}

.component-item:active {
    cursor: grabbing;
}

.canvas-area {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-20);
    display: flex;
    flex-direction: column;
}

.canvas-area h3 {
    margin: 0 0 var(--space-16) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.drop-zone {
    flex: 1;
    border: 2px dashed var(--color-border);
    border-radius: var(--radius-md);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--color-text-secondary);
    position: relative;
    background: var(--color-background);
}

.drop-zone.dragover {
    border-color: var(--color-primary);
    background: rgba(var(--color-primary-rgb), 0.05);
}

.demo-components {
    display: flex;
    gap: var(--space-16);
    margin-top: var(--space-20);
}

.demo-component {
    padding: var(--space-12) var(--space-16);
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
}

.property-group {
    margin-bottom: var(--space-16);
}

.property-group label {
    display: block;
    margin-bottom: var(--space-8);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

/* ETL Pipeline */
.pipeline-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-32);
    margin-bottom: var(--space-32);
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    margin-bottom: var(--space-4);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.pipeline-canvas {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-32);
    text-align: center;
}

.pipeline-flow {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-20);
    margin-bottom: var(--space-32);
}

.flow-node {
    background: var(--color-background);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-20);
    min-width: 140px;
    text-align: center;
}

.flow-node.source {
    border-color: var(--color-success);
}

.flow-node.transform {
    border-color: var(--color-primary);
}

.flow-node.destination {
    border-color: var(--color-warning);
}

.node-icon {
    font-size: 2rem;
    margin-bottom: var(--space-8);
}

.node-label {
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-4);
}

.node-detail {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
}

.flow-arrow {
    font-size: var(--font-size-2xl);
    color: var(--color-primary);
    font-weight: bold;
}

/* Templates Grid */
.templates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-24);
}

.template-card {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
    text-align: center;
    transition: all var(--duration-normal) var(--ease-standard);
}

.template-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.template-icon {
    font-size: 3rem;
    margin-bottom: var(--space-16);
}

.template-card h3 {
    margin: 0 0 var(--space-12) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.template-card p {
    margin: 0 0 var(--space-16) 0;
    color: var(--color-text-secondary);
    line-height: 1.6;
}

.template-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-8);
    justify-content: center;
    margin-bottom: var(--space-20);
}

.feature-tag {
    background: var(--color-secondary);
    color: var(--color-text-secondary);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
}

/* Marketplace */
.marketplace-search {
    display: flex;
    gap: var(--space-12);
    margin-bottom: var(--space-32);
    max-width: 600px;
}

.marketplace-search .form-control {
    flex: 1;
}

.connector-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-24);
}

.category-card {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.category-card h3 {
    margin: 0 0 var(--space-8) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.category-count {
    color: var(--color-text-secondary);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-16);
}

.popular-connectors {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-8);
}

.connector-tag {
    background: var(--color-secondary);
    color: var(--color-text);
    padding: var(--space-6) var(--space-12);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

/* Collaboration Features */
.collaboration-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-24);
}

.feature-card {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: var(--space-16);
}

.feature-card h3 {
    margin: 0 0 var(--space-12) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.feature-demo {
    margin-top: var(--space-16);
    padding: var(--space-16);
    background: var(--color-background);
    border-radius: var(--radius-md);
}

.comment-bubble {
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    padding: var(--space-12);
    border-radius: var(--radius-md);
    position: relative;
    max-width: 200px;
}

.comment-bubble::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid var(--color-primary);
}

.workspace-list, .version-history {
    margin-top: var(--space-16);
}

.workspace-item, .version-item {
    padding: var(--space-8) var(--space-12);
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    margin-bottom: var(--space-8);
    font-size: var(--font-size-sm);
}

/* Mobile Preview */
.mobile-preview {
    display: flex;
    justify-content: center;
    margin-top: var(--space-32);
}

.mobile-frame {
    background: #1a1a1a;
    border-radius: 25px;
    padding: var(--space-20);
    box-shadow: var(--shadow-lg);
}

.mobile-screen {
    width: 280px;
    height: 500px;
    background: var(--color-surface);
    border-radius: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.mobile-header-bar {
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    padding: var(--space-12) var(--space-16);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
}

.mobile-metrics {
    display: flex;
    justify-content: space-around;
    padding: var(--space-20);
}

.mobile-metric {
    text-align: center;
}

.mobile-metric .metric-value {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    display: block;
}

.mobile-metric .metric-label {
    font-size: var(--font-size-xs);
    color: var(--color-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.mobile-chart {
    flex: 1;
    padding: var(--space-16);
}

.mobile-chart canvas {
    width: 100% !important;
    height: 100% !important;
}

.mobile-actions {
    display: flex;
    gap: var(--space-12);
    padding: var(--space-16);
}

.mobile-btn {
    flex: 1;
    padding: var(--space-8);
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
}

/* Partner Portal */
.partner-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-32);
}

.branding-section, .revenue-section {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.branding-section h3, .revenue-section h3 {
    margin: 0 0 var(--space-20) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.brand-preview {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    padding: var(--space-20);
    margin-bottom: var(--space-16);
    text-align: center;
}

.brand-logo {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-text-secondary);
    margin-bottom: var(--space-16);
}

.brand-colors {
    display: flex;
    justify-content: center;
    gap: var(--space-8);
}

.color-swatch {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid var(--color-border);
}

.brand-controls {
    display: flex;
    gap: var(--space-12);
}

.revenue-calculator {
    background: var(--color-background);
    border: 1px solid var(--color-border);
    border-radius: var(--radius-md);
    padding: var(--space-20);
}

.calculator-input {
    margin-bottom: var(--space-16);
}

.calculator-input label {
    display: block;
    margin-bottom: var(--space-8);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.calculator-result {
    text-align: center;
}

.revenue-amount {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-success);
    display: block;
    margin-bottom: var(--space-4);
}

.revenue-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Cost Optimization */
.cost-savings-highlight {
    margin-bottom: var(--space-32);
}

.savings-card {
    background: linear-gradient(135deg, var(--color-success), var(--color-primary));
    color: white;
    border-radius: var(--radius-lg);
    padding: var(--space-32);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-24);
}

.savings-icon {
    font-size: 4rem;
}

.savings-content h3 {
    margin: 0 0 var(--space-8) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.savings-amount {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    display: block;
    margin-bottom: var(--space-4);
}

.savings-period {
    opacity: 0.8;
    font-size: var(--font-size-sm);
}

.cost-breakdown {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.cost-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-12) 0;
    border-bottom: 1px solid var(--color-border);
}

.cost-item:last-child {
    border-bottom: none;
}

.cost-category {
    font-weight: var(--font-weight-medium);
}

.cost-amount {
    font-weight: var(--font-weight-bold);
    color: var(--color-text);
}

.cost-trend {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
}

.cost-trend.up {
    background: rgba(var(--color-error-rgb), 0.1);
    color: var(--color-error);
}

.cost-trend.down {
    background: rgba(var(--color-success-rgb), 0.1);
    color: var(--color-success);
}

/* Security & Compliance */
.security-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-20);
    margin-bottom: var(--space-32);
}

.status-card {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-20);
    text-align: center;
}

.status-card.compliant {
    border-color: var(--color-success);
}

.status-card.secure {
    border-color: var(--color-primary);
}

.status-card.monitoring {
    border-color: var(--color-warning);
}

.status-icon {
    font-size: 2.5rem;
    margin-bottom: var(--space-12);
}

.status-card h3 {
    margin: 0 0 var(--space-8) 0;
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
}

.audit-trail {
    background: var(--color-surface);
    border: 1px solid var(--color-card-border);
    border-radius: var(--radius-lg);
    padding: var(--space-24);
}

.audit-trail h3 {
    margin: 0 0 var(--space-20) 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.audit-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-12) 0;
    border-bottom: 1px solid var(--color-border);
    font-size: var(--font-size-sm);
}

.audit-item:last-child {
    border-bottom: none;
}

.audit-time {
    color: var(--color-text-secondary);
    font-family: var(--font-family-mono);
}

.audit-action {
    font-weight: var(--font-weight-medium);
}

.audit-user {
    color: var(--color-primary);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--color-surface);
    border-radius: var(--radius-lg);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-20);
    border-bottom: 1px solid var(--color-border);
}

.modal-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
}

.modal-close {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    color: var(--color-text-secondary);
    padding: var(--space-4);
}

.modal-body {
    padding: var(--space-20);
    max-height: 60vh;
    overflow-y: auto;
}

.chat-container {
    margin-bottom: var(--space-20);
    min-height: 200px;
}

.chat-message {
    margin-bottom: var(--space-16);
    padding: var(--space-12);
    border-radius: var(--radius-md);
    max-width: 80%;
}

.chat-message.ai {
    background: var(--color-secondary);
    color: var(--color-text);
}

.chat-message.user {
    background: var(--color-primary);
    color: var(--color-btn-primary-text);
    margin-left: auto;
}

.chat-input {
    display: flex;
    gap: var(--space-12);
}

.chat-input .form-control {
    flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--duration-normal) var(--ease-standard);
    }

    .sidebar.open {
        transform: translateX(0);
    }

    .sidebar-toggle {
        display: block;
    }

    .main-content {
        margin-left: 0;
    }

    .header {
        padding: var(--space-16);
    }

    .content-section {
        padding: var(--space-16);
    }

    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .charts-section {
        grid-template-columns: 1fr;
    }

    .builder-workspace {
        grid-template-columns: 1fr;
        height: auto;
    }

    .component-palette, .properties-panel {
        order: 1;
    }

    .canvas-area {
        order: 2;
        min-height: 300px;
    }

    .partner-features {
        grid-template-columns: 1fr;
    }

    .pipeline-flow {
        flex-direction: column;
    }

    .flow-arrow {
        transform: rotate(90deg);
    }

    .modal-content {
        width: 95%;
        margin: var(--space-16);
    }
}

/* Theme Toggle Animation */
.theme-transition {
    transition: background-color var(--duration-normal) var(--ease-standard),
                color var(--duration-normal) var(--ease-standard),
                border-color var(--duration-normal) var(--ease-standard);
}

/* Glassmorphism Effects */
.glassmorphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Micro-interactions */
.btn, .nav-item, .metric-card, .insight-card, .template-card {
    transition: all var(--duration-normal) var(--ease-standard);
}

.btn:hover, .nav-item:hover, .metric-card:hover, .insight-card:hover, .template-card:hover {
    transform: translateY(-2px);
}

/* Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--color-primary);
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus States */
.btn:focus-visible,
.nav-item:focus-visible,
.form-control:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .sidebar,
    .header-right,
    .btn {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .content-section {
        display: block !important;
    }
}