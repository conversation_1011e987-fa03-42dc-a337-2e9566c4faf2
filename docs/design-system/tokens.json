{"colors": {"light": {"primary": {"value": "#21808D", "rgb": "33, 128, 141", "hover": "#1D7480", "active": "#1A6873"}, "background": {"value": "#FCFCF9", "rgb": "252, 252, 249"}, "surface": {"value": "#FFFFFD", "rgb": "255, 255, 253"}, "text": {"primary": {"value": "#13343B", "rgb": "19, 52, 59"}, "secondary": {"value": "#626C71", "rgb": "98, 108, 113"}}, "secondary": {"value": "rgba(94, 82, 64, 0.12)", "hover": "rgba(94, 82, 64, 0.2)", "active": "rgba(94, 82, 64, 0.25)"}, "semantic": {"error": {"value": "#C0152F", "rgb": "192, 21, 47"}, "success": {"value": "#21808D", "rgb": "33, 128, 141"}, "warning": {"value": "#A84B2F", "rgb": "168, 75, 47"}, "info": {"value": "#626C71", "rgb": "98, 108, 113"}}, "border": {"default": "rgba(94, 82, 64, 0.2)", "card": "rgba(94, 82, 64, 0.12)"}}, "dark": {"primary": {"value": "#32B8C6", "rgb": "50, 184, 198", "hover": "#2DA6B2", "active": "#2996A1"}, "background": {"value": "#1F2121", "rgb": "31, 33, 33"}, "surface": {"value": "#262828", "rgb": "38, 40, 40"}, "text": {"primary": {"value": "#F5F5F5", "rgb": "245, 245, 245"}, "secondary": {"value": "rgba(167, 169, 169, 0.7)", "rgb": "167, 169, 169"}}, "secondary": {"value": "rgba(119, 124, 124, 0.15)", "hover": "rgba(119, 124, 124, 0.25)", "active": "rgba(119, 124, 124, 0.3)"}, "semantic": {"error": {"value": "#FF5459", "rgb": "255, 84, 89"}, "success": {"value": "#32B8C6", "rgb": "50, 184, 198"}, "warning": {"value": "#E68161", "rgb": "230, 129, 97"}, "info": {"value": "#A7A9A9", "rgb": "167, 169, 169"}}, "border": {"default": "rgba(119, 124, 124, 0.3)", "card": "rgba(119, 124, 124, 0.2)"}}}, "typography": {"fontFamily": {"base": "\"FKGroteskNeue\", \"Geist\", \"Inter\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif", "mono": "\"Berkeley Mono\", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, monospace"}, "fontSize": {"xs": "11px", "sm": "12px", "base": "14px", "md": "14px", "lg": "16px", "xl": "18px", "2xl": "20px", "3xl": "24px", "4xl": "30px"}, "fontWeight": {"normal": 400, "medium": 500, "semibold": 550, "bold": 600}, "lineHeight": {"tight": 1.2, "normal": 1.5}, "letterSpacing": {"tight": "-0.01em"}}, "spacing": {"0": "0", "1": "1px", "2": "2px", "4": "4px", "6": "6px", "8": "8px", "10": "10px", "12": "12px", "16": "16px", "20": "20px", "24": "24px", "28": "28px", "32": "32px", "40": "40px", "48": "48px", "56": "56px", "64": "64px"}, "borderRadius": {"sm": "6px", "base": "8px", "md": "10px", "lg": "12px", "xl": "16px", "full": "9999px"}, "shadows": {"xs": "0 1px 2px rgba(0, 0, 0, 0.02)", "sm": "0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02)", "md": "0 4px 6px -1px rgba(0, 0, 0, 0.04), 0 2px 4px -1px rgba(0, 0, 0, 0.02)", "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.04), 0 4px 6px -2px rgba(0, 0, 0, 0.02)", "inset": "inset 0 1px 0 rgba(255, 255, 255, 0.15), inset 0 -1px 0 rgba(0, 0, 0, 0.03)"}, "animation": {"duration": {"fast": "150ms", "normal": "250ms"}, "easing": {"standard": "cubic-bezier(0.16, 1, 0.3, 1)"}}, "breakpoints": {"sm": "640px", "md": "768px", "lg": "1024px", "xl": "1280px", "2xl": "1536px"}, "zIndex": {"dropdown": 1000, "sticky": 1020, "fixed": 1030, "modalBackdrop": 1040, "modal": 1050, "popover": 1060, "tooltip": 1070}}