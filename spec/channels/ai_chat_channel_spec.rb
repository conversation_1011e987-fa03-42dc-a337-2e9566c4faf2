require 'rails_helper'

RSpec.describe AiChatChannel, type: :channel do
  let(:organization) { create(:organization) }
  let(:user) { create(:user, organization: organization) }
  let(:conversation) { create(:ai_conversation, organization: organization, user: user) }

  before do
    stub_connection current_user: user
  end

  describe '#subscribed' do
    context 'with valid conversation' do
      it 'subscribes to organization and user streams' do
        subscribe(conversation_id: conversation.id)
        
        expect(subscription).to be_confirmed
        expect(subscription).to have_stream_from("ai_chat:organization:#{organization.id}")
        expect(subscription).to have_stream_from("ai_chat:user:#{user.id}")
      end
    end

    context 'with unauthorized conversation' do
      let(:other_org) { create(:organization) }
      let(:other_conversation) { create(:ai_conversation, organization: other_org) }

      it 'rejects subscription' do
        subscribe(conversation_id: other_conversation.id)
        expect(subscription).to be_rejected
      end
    end

    context 'without conversation_id' do
      it 'rejects subscription' do
        subscribe
        expect(subscription).to be_rejected
      end
    end
  end

  describe '#receive' do
    before do
      subscribe(conversation_id: conversation.id)
    end

    context 'typing indicator' do
      it 'broadcasts typing status to organization' do
        expect { perform :receive, action: 'typing', typing: true }.to(
          have_broadcasted_to("ai_chat:organization:#{organization.id}").with(
            hash_including(
              action: 'typing_indicator',
              user_id: user.id,
              conversation_id: conversation.id,
              typing: true
            )
          )
        )
      end
    end

    context 'mark messages as read' do
      let!(:messages) do
        3.times.map do
          create(:ai_message, conversation: conversation, role: 'assistant', read_at: nil)
        end
      end

      it 'marks all unread messages as read' do
        expect {
          perform :receive, action: 'mark_read', conversation_id: conversation.id
        }.to change {
          messages.map(&:reload).all? { |m| m.read_at.present? }
        }.from(false).to(true)
      end

      it 'broadcasts read status update' do
        expect {
          perform :receive, action: 'mark_read', conversation_id: conversation.id
        }.to have_broadcasted_to("ai_chat:user:#{user.id}").with(
          hash_including(
            action: 'messages_read',
            conversation_id: conversation.id
          )
        )
      end
    end
  end

  describe '#unsubscribed' do
    it 'stops all streams' do
      subscribe(conversation_id: conversation.id)
      expect(subscription).to have_stream_from("ai_chat:organization:#{organization.id}")
      
      unsubscribe
      expect(subscription).not_to have_streams
    end
  end
end