require 'rails_helper'

RSpec.describe ApplicationCable::Connection, type: :channel do
  let(:user) { create(:user) }
  let(:env) { {} }
  let(:warden) { double('warden') }

  before do
    allow_any_instance_of(ApplicationCable::Connection).to receive(:env).and_return(env)
    allow(env).to receive(:[]).with('warden').and_return(warden)
  end

  describe 'authentication' do
    context 'with authenticated user' do
      before do
        allow(warden).to receive(:user).and_return(user)
      end

      it 'successfully connects' do
        connection = ApplicationCable::Connection.new(ActionCable.server, env)
        expect { connection.connect }.not_to raise_error
        expect(connection.current_user).to eq(user)
      end
    end

    context 'without authenticated user' do
      before do
        allow(warden).to receive(:user).and_return(nil)
      end

      it 'rejects the connection' do
        connection = ApplicationCable::Connection.new(ActionCable.server, env)
        expect { connection.connect }.to raise_error(ActionCable::Connection::Authorization::UnauthorizedError)
      end
    end
  end
end