<% content_for :page_title, "Audit Logs" %>
<% content_for :page_subtitle, "Track all actions and changes within your organization" %>

<div class="dashboard-content">
  <!-- Audit Logs Section -->
  <section class="content-section active" id="audit-logs">

    <!-- Header with Back Button -->
    <div class="flex items-center justify-between mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">Audit Logs</h1>
        <p class="text-gray-600 dark:text-gray-400">Comprehensive activity monitoring and security tracking</p>
      </div>
      <%= link_to organization_path, class: "btn btn--outline btn--sm" do %>
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
        Back to Organization
      <% end %>
    </div>

    <!-- Filters Panel -->
    <div class="ai-insights-panel mb-8">
      <h2 class="flex items-center gap-2 mb-6">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"/>
        </svg>
        Filter Activity
      </h2>

      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4 mb-6">
        <!-- Action Type Filter -->
        <div>
          <label for="action-filter" class="block text-sm font-medium mb-2" style="color: var(--color-text-secondary);">Action Type</label>
          <select id="action-filter" name="action" class="w-full px-3 py-2 text-sm border-0 focus:ring-2 focus:ring-offset-0" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); color: var(--color-text); focus:ring-color: var(--color-primary);">
            <option value="">All Actions</option>
            <option value="create">Create</option>
            <option value="update">Update</option>
            <option value="delete">Delete</option>
            <option value="login">Login</option>
            <option value="logout">Logout</option>
          </select>
        </div>

        <!-- Resource Type Filter -->
        <div>
          <label for="resource-filter" class="block text-sm font-medium mb-2" style="color: var(--color-text-secondary);">Resource Type</label>
          <select id="resource-filter" name="resource_type" class="w-full px-3 py-2 text-sm border-0 focus:ring-2 focus:ring-offset-0" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); color: var(--color-text); focus:ring-color: var(--color-primary);">
            <option value="">All Resources</option>
            <option value="User">Users</option>
            <option value="DataSource">Data Sources</option>
            <option value="Organization">Organization</option>
            <option value="ExtractionJob">Extraction Jobs</option>
          </select>
        </div>

        <!-- From Date Filter -->
        <div>
          <label for="date-from" class="block text-sm font-medium mb-2" style="color: var(--color-text-secondary);">From Date</label>
          <input type="date" id="date-from" name="date_from" class="w-full px-3 py-2 text-sm border-0 focus:ring-2 focus:ring-offset-0" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); color: var(--color-text); focus:ring-color: var(--color-primary);">
        </div>

        <!-- To Date Filter -->
        <div>
          <label for="date-to" class="block text-sm font-medium mb-2" style="color: var(--color-text-secondary);">To Date</label>
          <input type="date" id="date-to" name="date_to" class="w-full px-3 py-2 text-sm border-0 focus:ring-2 focus:ring-offset-0" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); color: var(--color-text); focus:ring-color: var(--color-primary);">
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-wrap gap-3">
        <button type="button" class="btn btn--primary btn--sm">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"/>
          </svg>
          Apply Filters
        </button>

        <button type="button" class="btn btn--outline btn--sm">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Clear Filters
        </button>

        <button type="button" class="btn btn--outline btn--sm">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
          Export CSV
        </button>
      </div>
    </div>

    <!-- Audit Logs Table -->
    <div class="chart-container">
      <div class="chart-header">
        <h3 class="flex items-center gap-2">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"/>
          </svg>
          Recent Activity
        </h3>
        <div class="flex items-center gap-2">
          <span class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            Live
          </span>
        </div>
      </div>
      
      <% if defined?(@audit_logs) && @audit_logs.any? %>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead style="background-color: var(--color-surface); border-bottom: 1px solid var(--color-card-border);">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--color-text-secondary);">
                  <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Timestamp
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--color-text-secondary);">
                  <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    User
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--color-text-secondary);">
                  <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                    Action
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--color-text-secondary);">
                  <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                    </svg>
                    Resource
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--color-text-secondary);">
                  <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    Details
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" style="color: var(--color-text-secondary);">
                  <div class="flex items-center gap-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
                    </svg>
                    IP Address
                  </div>
                </th>
              </tr>
            </thead>
            <tbody style="background-color: var(--color-background); border-top: 1px solid var(--color-card-border);">
              <% @audit_logs.each do |log| %>
                <tr class="premium-table-row group hover:bg-white/80 hover:shadow-lg transition-all duration-300 hover:scale-[1.01] hover:z-10 relative">
                  <td class="px-8 py-6 whitespace-nowrap">
                    <div class="flex flex-col">
                      <span class="text-sm font-semibold text-gray-900">
                        <%= log.performed_at.strftime("%b %d, %Y") %>
                      </span>
                      <span class="text-xs text-gray-500 font-medium">
                        <%= log.performed_at.strftime("%l:%M %p") %>
                      </span>
                    </div>
                  </td>
                  <td class="px-8 py-6 whitespace-nowrap">
                    <div class="flex items-center group-hover:scale-105 transition-transform duration-200">
                      <div class="flex-shrink-0 h-10 w-10 relative">
                        <div class="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-400 to-purple-500 flex items-center justify-center shadow-lg ring-2 ring-white">
                          <span class="text-sm font-bold text-white">
                            <%= log.user&.first_name&.first || 'S' %>
                          </span>
                        </div>
                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-semibold text-gray-900">
                          <%= log.user&.first_name || 'System' %> <%= log.user&.last_name %>
                        </div>
                        <div class="text-xs text-gray-500 font-medium">
                          <%= log.user&.email || '<EMAIL>' %>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-8 py-6 whitespace-nowrap">
                    <%
                      action_config = case log.action
                      when 'create'
                        { bg: 'from-emerald-100 to-green-100', text: 'text-emerald-800', ring: 'ring-emerald-200', icon: 'M12 6v6m0 0v6m0-6h6m-6 0H6' }
                      when 'update'
                        { bg: 'from-blue-100 to-indigo-100', text: 'text-blue-800', ring: 'ring-blue-200', icon: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z' }
                      when 'delete'
                        { bg: 'from-red-100 to-pink-100', text: 'text-red-800', ring: 'ring-red-200', icon: 'M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16' }
                      when 'login'
                        { bg: 'from-indigo-100 to-purple-100', text: 'text-indigo-800', ring: 'ring-indigo-200', icon: 'M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1' }
                      else
                        { bg: 'from-gray-100 to-slate-100', text: 'text-gray-800', ring: 'ring-gray-200', icon: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' }
                      end
                    %>
                    <span class="inline-flex items-center gap-2 rounded-xl bg-gradient-to-r <%= action_config[:bg] %> px-4 py-2 text-xs font-bold <%= action_config[:text] %> ring-1 ring-inset <%= action_config[:ring] %> shadow-sm group-hover:shadow-md transition-all duration-200">
                      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<%= action_config[:icon] %>"/>
                      </svg>
                      <%= log.action.humanize %>
                    </span>
                  </td>
                  <td class="px-8 py-6 whitespace-nowrap">
                    <div class="flex flex-col">
                      <span class="text-sm font-semibold text-gray-900">
                        <%= log.resource_type %>
                      </span>
                      <% if log.resource_id %>
                        <span class="text-xs text-gray-500 font-medium">
                          ID: #<%= log.resource_id %>
                        </span>
                      <% end %>
                    </div>
                  </td>
                  <td class="px-8 py-6">
                    <div class="max-w-xs">
                      <p class="text-sm text-gray-900 font-medium truncate">
                        <%= log.details || 'No additional details' %>
                      </p>
                      <% if log.details && log.details.length > 50 %>
                        <button class="text-xs text-indigo-600 hover:text-indigo-800 font-medium mt-1 transition-colors duration-200">
                          View full details
                        </button>
                      <% end %>
                    </div>
                  </td>
                  <td class="px-8 py-6 whitespace-nowrap">
                    <div class="flex items-center gap-2">
                      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9 3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
                      </svg>
                      <span class="text-sm text-gray-600 font-mono">
                        <%= log.ip_address || 'N/A' %>
                      </span>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>

        <!-- Premium Pagination -->
        <div class="px-8 py-6 border-t border-gray-200/50 bg-gradient-to-r from-gray-50/50 to-blue-50/50">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
              </svg>
              <span class="text-sm text-gray-700 font-medium">
                Showing <span class="font-bold text-indigo-600">1</span> to <span class="font-bold text-indigo-600"><%= @audit_logs.count %></span> of
                <span class="font-bold text-indigo-600"><%= @audit_logs.count %></span> results
              </span>
            </div>
            <div class="flex items-center gap-3">
              <button type="button" class="premium-pagination-btn group inline-flex items-center gap-2 px-4 py-2 text-sm font-semibold rounded-lg transition-all duration-300 hover:scale-105 bg-white/80 text-gray-700 hover:text-gray-900 border border-gray-300 hover:border-gray-400 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed">
                <svg class="w-4 h-4 transition-transform group-hover:-translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                </svg>
                Previous
              </button>

              <div class="flex items-center gap-1">
                <button class="w-8 h-8 rounded-lg bg-gradient-to-r from-indigo-600 to-purple-600 text-white text-sm font-bold shadow-lg">1</button>
                <button class="w-8 h-8 rounded-lg bg-white/80 hover:bg-white text-gray-700 hover:text-gray-900 text-sm font-semibold transition-all duration-200 hover:shadow-md">2</button>
                <button class="w-8 h-8 rounded-lg bg-white/80 hover:bg-white text-gray-700 hover:text-gray-900 text-sm font-semibold transition-all duration-200 hover:shadow-md">3</button>
                <span class="px-2 text-gray-500">...</span>
                <button class="w-8 h-8 rounded-lg bg-white/80 hover:bg-white text-gray-700 hover:text-gray-900 text-sm font-semibold transition-all duration-200 hover:shadow-md">10</button>
              </div>

              <button type="button" class="premium-pagination-btn group inline-flex items-center gap-2 px-4 py-2 text-sm font-semibold rounded-lg transition-all duration-300 hover:scale-105 bg-white/80 text-gray-700 hover:text-gray-900 border border-gray-300 hover:border-gray-400 hover:shadow-md">
                Next
                <svg class="w-4 h-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                </svg>
              </button>
            </div>
          </div>
        </div>
      <% else %>
        <!-- Empty State -->
        <div class="px-8 py-20 text-center">
          <div class="mx-auto w-12 h-12 rounded-lg flex items-center justify-center mb-6" style="background-color: var(--color-surface); border: 1px solid var(--color-card-border);">
            <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="color: var(--color-text-secondary);">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
            </svg>
          </div>

          <h3 class="text-lg font-semibold mb-3" style="color: var(--color-text);">No audit logs found</h3>

          <p class="text-sm max-w-sm mx-auto mb-8" style="color: var(--color-text-secondary); line-height: 1.5;">
            Activity logs will appear here as actions are performed within your organization.
            Start by creating users, connecting data sources, or managing your organization settings.
          </p>

          <button class="btn btn--outline btn--sm inline-flex items-center gap-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
            </svg>
            Refresh Activity
          </button>
        </div>
      <% end %>
    </div>
  </div>
</div>