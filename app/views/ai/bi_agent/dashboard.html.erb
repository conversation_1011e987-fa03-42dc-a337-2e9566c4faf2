<% content_for :page_title, "Business Intelligence Agent" %>
<% content_for :page_subtitle, "Autonomous insights and analytics powered by AI" %>

<div class="dashboard-content">
  <!-- BI Agent Section -->
  <section class="content-section active" id="bi-agent" data-controller="bi-agent">
  <!-- BI Agent Header -->
  <div class="bi-agent-header">
    <div class="bi-agent-header-content">
      <div class="bi-agent-title-section">
        <div class="bi-agent-icon">
          <svg width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="bi-agent-title-text">
          <h2>Business Intelligence Agent</h2>
          <p>Autonomous insights and analytics powered by AI</p>
        </div>
      </div>
      <div class="bi-agent-controls">
        <% if @agent_status[:status] == 'active' %>
          <button id="stop-agent-btn" class="btn btn--danger">
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path>
            </svg>
            Stop Agent
          </button>
        <% else %>
          <button id="start-agent-btn" class="btn btn--primary">
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            Start Agent
          </button>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Agent Status Card -->
  <div class="metric-card agent-status-card">
    <div class="agent-status-content">
      <div class="agent-status-main">
        <div class="agent-status-icon">
          <% if @agent_status[:status] == 'active' %>
            <div class="status-icon status-icon--active">
              <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <div class="status-pulse"></div>
            </div>
          <% else %>
            <div class="status-icon status-icon--inactive">
              <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
            </div>
          <% end %>
        </div>
        <div class="agent-status-info">
          <h3>Agent Status</h3>
          <p class="agent-status-value <%= @agent_status[:status] == 'active' ? 'status-active' : 'status-inactive' %>">
            <%= @agent_status[:status].capitalize %>
          </p>
        </div>
      </div>
      <div class="agent-status-meta">
        <p class="meta-label">Last updated</p>
        <p class="meta-value">
          <%= @agent_status[:last_updated]&.strftime('%B %d, %Y at %I:%M %p') || 'Never' %>
        </p>
      </div>
    </div>
  </div>

  <!-- BI Agent Metrics Grid -->
  <div class="metrics-grid">
    <!-- Insights Generated -->
    <div class="metric-card">
      <div class="metric-icon">📊</div>
      <div class="metric-content">
        <h3>Insights Generated</h3>
        <p class="metric-value"><%= @agent_status[:insights_count] || 0 %></p>
        <p class="metric-change positive">+12% vs last week</p>
      </div>
    </div>

    <!-- Agent Uptime -->
    <div class="metric-card">
      <div class="metric-icon">⏱️</div>
      <div class="metric-content">
        <h3>Agent Uptime</h3>
        <p class="metric-value"><%= @agent_status[:uptime] || '0h' %></p>
        <p class="metric-change neutral">Continuous monitoring</p>
      </div>
    </div>

    <!-- Weekly Reports -->
    <div class="metric-card">
      <div class="metric-icon">📋</div>
      <div class="metric-content">
        <h3>Weekly Reports</h3>
        <p class="metric-value"><%= @weekly_reports.count %></p>
        <p class="metric-change neutral">Automated summaries</p>
      </div>
    </div>

    <!-- Data Sources -->
    <div class="metric-card">
      <div class="metric-icon">🔗</div>
      <div class="metric-content">
        <h3>Data Sources</h3>
        <p class="metric-value"><%= current_organization.data_sources.count %></p>
        <p class="metric-change neutral">Connected platforms</p>
      </div>
    </div>
  </div>

  <!-- Recent Insights Section -->
  <div class="content-card">
    <div class="content-card-header">
      <div class="content-card-title">
        <div class="content-card-icon">💡</div>
        <div>
          <h3>Recent Insights</h3>
          <p>Latest AI-generated business intelligence</p>
        </div>
      </div>
    </div>
    <div class="content-card-body">
      <% if @recent_insights.any? %>
        <div class="insights-list">
          <% @recent_insights.each do |insight| %>
            <div class="insight-item">
              <div class="insight-icon">💡</div>
              <div class="insight-content">
                <h4><%= insight[:title] %></h4>
                <p><%= insight[:description] %></p>
              </div>
              <div class="insight-meta">
                <span class="insight-priority priority-<%= insight[:priority] %>"><%= insight[:priority].capitalize %></span>
                <span class="insight-date"><%= insight[:created_at]&.strftime('%m/%d/%Y') %></span>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="empty-state">
          <div class="empty-state-icon">💡</div>
          <p>No insights generated yet. Start the agent to begin generating insights.</p>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Weekly Reports Section -->
  <div class="content-card">
    <div class="content-card-header">
      <div class="content-card-title">
        <div class="content-card-icon">📋</div>
        <div>
          <h3>Weekly Reports</h3>
          <p>Automated business intelligence summaries</p>
        </div>
      </div>
    </div>
    <div class="content-card-body">
      <% if @weekly_reports.any? %>
        <div class="reports-list">
          <% @weekly_reports.each do |report| %>
            <div class="report-item">
              <div class="report-icon">📋</div>
              <div class="report-content">
                <h4><%= report[:title] %></h4>
                <p><%= report[:summary] %></p>
              </div>
              <div class="report-meta">
                <span class="report-date"><%= report[:created_at]&.strftime('%m/%d/%Y') %></span>
                <a href="#" class="btn btn--sm btn--primary">View</a>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="empty-state">
          <div class="empty-state-icon">📋</div>
          <p>No weekly reports available yet.</p>
        </div>
      <% end %>
    </div>
  </div>
  </section>
</div>



<script>
document.addEventListener('turbo:load', function() {
  const startBtn = document.getElementById('start-agent-btn');
  const stopBtn = document.getElementById('stop-agent-btn');

  if (startBtn) {
    startBtn.addEventListener('click', function() {
      this.disabled = true;
      this.textContent = 'Starting...';

      fetch('/ai/bi_agent/start_agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          location.reload();
        } else {
          alert('Failed to start agent: ' + data.error);
          this.disabled = false;
          this.innerHTML = '<svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>Start Agent';
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to start agent');
        this.disabled = false;
        this.innerHTML = '<svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>Start Agent';
      });
    });
  }

  if (stopBtn) {
    stopBtn.addEventListener('click', function() {
      this.disabled = true;
      this.textContent = 'Stopping...';

      fetch('/ai/bi_agent/stop_agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          location.reload();
        } else {
          alert('Failed to stop agent: ' + data.error);
          this.disabled = false;
          this.innerHTML = '<svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path></svg>Stop Agent';
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to stop agent');
        this.disabled = false;
        this.innerHTML = '<svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path></svg>Stop Agent';
      });
    });
  }
});
</script>