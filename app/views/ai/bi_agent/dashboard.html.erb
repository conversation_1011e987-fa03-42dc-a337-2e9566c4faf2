<% content_for :page_title, "Business Intelligence Agent" %>
<% content_for :page_subtitle, "Autonomous insights and analytics powered by AI" %>

<div class="dashboard-content">
  <!-- BI Agent Section -->
  <section class="content-section active" id="bi-agent" data-controller="bi-agent">
  <!-- BI Agent Header -->
  <div class="bi-agent-header">
    <div class="bi-agent-header-content">
      <div class="bi-agent-title-section">
        <div class="bi-agent-icon">
          <svg width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
        </div>
        <div class="bi-agent-title-text">
          <h2>Business Intelligence Agent</h2>
          <p>Autonomous insights and analytics powered by AI</p>
        </div>
      </div>
      <div class="bi-agent-controls">
        <% if @agent_status[:status] == 'active' %>
          <button id="stop-agent-btn" class="btn btn--danger">
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path>
            </svg>
            Stop Agent
          </button>
        <% else %>
          <button id="start-agent-btn" class="btn btn--primary">
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            Start Agent
          </button>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Agent Status Card -->
  <div class="metric-card agent-status-card">
    <div class="agent-status-content">
      <div class="agent-status-main">
        <div class="agent-status-icon">
          <% if @agent_status[:status] == 'active' %>
            <div class="status-icon status-icon--active">
              <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <div class="status-pulse"></div>
            </div>
          <% else %>
            <div class="status-icon status-icon--inactive">
              <svg width="24" height="24" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
            </div>
          <% end %>
        </div>
        <div class="agent-status-info">
          <h3>Agent Status</h3>
          <p class="agent-status-value <%= @agent_status[:status] == 'active' ? 'status-active' : 'status-inactive' %>">
            <%= @agent_status[:status].capitalize %>
          </p>
        </div>
      </div>
      <div class="agent-status-meta">
        <p class="meta-label">Last updated</p>
        <p class="meta-value">
          <%= @agent_status[:last_updated]&.strftime('%B %d, %Y at %I:%M %p') || 'Never' %>
        </p>
      </div>
    </div>
  </div>

  <!-- BI Agent Metrics Grid -->
  <div class="metrics-grid">
    <!-- Insights Generated -->
    <div class="metric-card">
      <div class="metric-icon">📊</div>
      <div class="metric-content">
        <h3>Insights Generated</h3>
        <p class="metric-value"><%= @agent_status[:insights_count] || 0 %></p>
        <p class="metric-change positive">+12% vs last week</p>
      </div>
    </div>

    <!-- Agent Uptime -->
    <div class="metric-card">
      <div class="metric-icon">⏱️</div>
      <div class="metric-content">
        <h3>Agent Uptime</h3>
        <p class="metric-value"><%= @agent_status[:uptime] || '0h' %></p>
        <p class="metric-change neutral">Continuous monitoring</p>
      </div>
    </div>

    <!-- Weekly Reports -->
    <div class="metric-card">
      <div class="metric-icon">📋</div>
      <div class="metric-content">
        <h3>Weekly Reports</h3>
        <p class="metric-value"><%= @weekly_reports.count %></p>
        <p class="metric-change neutral">Automated summaries</p>
      </div>
    </div>

    <!-- Data Sources -->
    <div class="metric-card">
      <div class="metric-icon">🔗</div>
      <div class="metric-content">
        <h3>Data Sources</h3>
        <p class="metric-value"><%= current_organization.data_sources.count %></p>
        <p class="metric-change neutral">Connected platforms</p>
      </div>
    </div>
  </div>

  <!-- Recent Insights Section -->
  <div class="content-card insights-section">
    <div class="content-card-header">
      <div class="content-card-title">
        <div class="content-card-icon insights-icon">
          <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
        </div>
        <div class="content-card-title-text">
          <h3>Recent Insights</h3>
          <p>Latest AI-generated business intelligence</p>
        </div>
      </div>
      <div class="content-card-actions">
        <button class="btn btn--sm btn--secondary">View All</button>
      </div>
    </div>
    <div class="content-card-body">
      <% if @recent_insights.any? %>
        <div class="insights-grid">
          <% @recent_insights.each_with_index do |insight, index| %>
            <div class="insight-card" data-priority="<%= insight[:priority] %>">
              <div class="insight-card-header">
                <div class="insight-priority-badge priority-<%= insight[:priority] %>">
                  <span class="priority-indicator"></span>
                  <span class="priority-text"><%= insight[:priority].capitalize %></span>
                </div>
                <div class="insight-timestamp">
                  <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12,6 12,12 16,14"></polyline>
                  </svg>
                  <span><%= insight[:created_at]&.strftime('%m/%d') %></span>
                </div>
              </div>
              <div class="insight-card-body">
                <div class="insight-icon-wrapper">
                  <div class="insight-type-icon">
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                </div>
                <div class="insight-content">
                  <h4 class="insight-title"><%= insight[:title] %></h4>
                  <p class="insight-description"><%= insight[:description] %></p>
                </div>
              </div>
              <div class="insight-card-footer">
                <div class="insight-tags">
                  <span class="insight-tag">AI Generated</span>
                </div>
                <button class="insight-action-btn" aria-label="View insight details">
                  <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="empty-state insights-empty">
          <div class="empty-state-visual">
            <div class="empty-state-icon">
              <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </svg>
            </div>
            <div class="empty-state-pulse"></div>
          </div>
          <div class="empty-state-content">
            <h4>No insights generated yet</h4>
            <p>Start the agent to begin generating AI-powered business insights</p>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Weekly Reports Section -->
  <div class="content-card reports-section">
    <div class="content-card-header">
      <div class="content-card-title">
        <div class="content-card-icon reports-icon">
          <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <div class="content-card-title-text">
          <h3>Weekly Reports</h3>
          <p>Automated business intelligence summaries</p>
        </div>
      </div>
      <div class="content-card-actions">
        <button class="btn btn--sm btn--secondary">View All</button>
      </div>
    </div>
    <div class="content-card-body">
      <% if @weekly_reports.any? %>
        <div class="reports-grid">
          <% @weekly_reports.each_with_index do |report, index| %>
            <div class="report-card">
              <div class="report-card-header">
                <div class="report-status-indicator">
                  <div class="status-dot status-ready"></div>
                  <span class="status-text">Ready</span>
                </div>
                <div class="report-date">
                  <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                  <span><%= report[:created_at]&.strftime('%b %d') %></span>
                </div>
              </div>
              <div class="report-card-body">
                <div class="report-icon-wrapper">
                  <div class="report-type-icon">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                </div>
                <div class="report-content">
                  <h4 class="report-title"><%= report[:title] %></h4>
                  <p class="report-summary"><%= report[:summary] %></p>
                </div>
              </div>
              <div class="report-card-footer">
                <div class="report-metrics">
                  <div class="report-metric">
                    <span class="metric-label">Insights</span>
                    <span class="metric-value">12</span>
                  </div>
                  <div class="report-metric">
                    <span class="metric-label">Pages</span>
                    <span class="metric-value">8</span>
                  </div>
                </div>
                <div class="report-actions">
                  <button class="report-action-btn secondary" aria-label="Download report">
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </button>
                  <a href="#" class="btn btn--sm btn--primary report-view-btn">
                    <svg width="14" height="14" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    View Report
                  </a>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="empty-state reports-empty">
          <div class="empty-state-visual">
            <div class="empty-state-icon">
              <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div class="empty-state-pulse"></div>
          </div>
          <div class="empty-state-content">
            <h4>No reports available yet</h4>
            <p>Weekly reports will appear here once the agent starts generating insights</p>
          </div>
        </div>
      <% end %>
    </div>
  </div>
  </section>
</div>



<script>
document.addEventListener('turbo:load', function() {
  const startBtn = document.getElementById('start-agent-btn');
  const stopBtn = document.getElementById('stop-agent-btn');

  if (startBtn) {
    startBtn.addEventListener('click', function() {
      this.disabled = true;
      this.textContent = 'Starting...';

      fetch('/ai/bi_agent/start_agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          location.reload();
        } else {
          alert('Failed to start agent: ' + data.error);
          this.disabled = false;
          this.innerHTML = '<svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>Start Agent';
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to start agent');
        this.disabled = false;
        this.innerHTML = '<svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" /></svg>Start Agent';
      });
    });
  }

  if (stopBtn) {
    stopBtn.addEventListener('click', function() {
      this.disabled = true;
      this.textContent = 'Stopping...';

      fetch('/ai/bi_agent/stop_agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          location.reload();
        } else {
          alert('Failed to stop agent: ' + data.error);
          this.disabled = false;
          this.innerHTML = '<svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path></svg>Stop Agent';
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Failed to stop agent');
        this.disabled = false;
        this.innerHTML = '<svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path></svg>Stop Agent';
      });
    });
  }
});
</script>