<% content_for :page_title, "Connect #{params[:source_type]&.humanize || 'Data Source'}" %>
<% content_for :page_subtitle, "Set up your integration in just a few steps" %>

<%
  # Initialize data for the wizard
  @wizard_data = DataSourceWizardService.new.prepare_wizard_data
  data_source_configs = @wizard_data[:configurations]
  sync_frequencies = @wizard_data[:sync_frequencies]
  file_config = @wizard_data[:file_config]
  
  # Pre-select source type if provided
  @data_source.source_type = params[:source_type] if params[:source_type].present?
%>

<div class="dashboard-content data-source-wizard">
  <div class="wizard-container" 
       data-controller="data-source-wizard auto-save" 
       data-auto-save-url-value="<%= auto_save_data_sources_path %>">
    
    <!-- Progress Section -->
    <div class="wizard-progress">
      <div class="progress-header">
        <div class="progress-info">
          <h2 data-data-source-wizard-target="stepTitle">Choose Your Platform</h2>
          <p data-data-source-wizard-target="stepDescription">Select the platform you want to connect</p>
        </div>
        <div class="progress-stats">
          <span class="step-indicator">
            Step <span data-data-source-wizard-target="currentStep">1</span> of 4
          </span>
          <span class="progress-percent" data-data-source-wizard-target="progressPercent">25%</span>
        </div>
      </div>
      
      <!-- Progress Bar -->
      <div class="progress-bar-container">
        <div class="progress-bar">
          <div class="progress-fill" 
               data-data-source-wizard-target="progressBar" 
               style="width: 25%"></div>
        </div>
      </div>
      
      <!-- Step Indicators -->
      <div class="step-indicators">
        <div class="step-item active" data-step="1">
          <div class="step-number">
            <span>1</span>
            <svg class="step-check" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <span class="step-label">Platform</span>
        </div>
        
        <div class="step-connector"></div>
        
        <div class="step-item" data-step="2">
          <div class="step-number">
            <span>2</span>
            <svg class="step-check" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <span class="step-label">Configure</span>
        </div>
        
        <div class="step-connector"></div>
        
        <div class="step-item" data-step="3">
          <div class="step-number">
            <span>3</span>
            <svg class="step-check" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <span class="step-label">Preview</span>
        </div>
        
        <div class="step-connector"></div>
        
        <div class="step-item" data-step="4">
          <div class="step-number">
            <span>4</span>
            <svg class="step-check" width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <span class="step-label">Launch</span>
        </div>
      </div>
    </div>

    <!-- Form Container -->
    <div class="wizard-form-container">
      <%= form_with model: @data_source, local: true, 
                    data: { 
                      data_source_wizard_target: "form",
                      auto_save_target: "form",
                      turbo: false 
                    }, 
                    class: "wizard-form" do |form| %>
        
        <!-- Auto-save Status -->
        <div class="autosave-status" data-auto-save-target="status">
          <div class="status-indicator">
            <span class="status-dot"></span>
            <span class="status-text">All changes saved</span>
          </div>
          <div class="status-time" data-auto-save-target="timestamp">
            Last saved: <span data-auto-save-target="time">just now</span>
          </div>
        </div>
        
        <!-- Step 1: Platform Selection -->
        <div class="wizard-step active" data-data-source-wizard-target="step1" data-step="1">
          <div class="step-content">
            <h3 class="step-title">Select Your Integration Platform</h3>
            <p class="step-subtitle">Choose from our supported platforms to start syncing your data</p>
            
            <div class="platforms-grid">
              <% data_source_configs.reject { |k, v| v[:status] == 'coming_soon' }.each do |key, config| %>
                <div class="platform-card <%= 'selected' if @data_source.source_type == key.to_s %>" 
                     data-source-type="<%= key %>"
                     data-action="click->data-source-wizard#selectPlatform">
                  <div class="platform-icon <%= key %>">
                    <%= config[:icon] || '🔗' %>
                  </div>
                  <h4><%= config[:name] %></h4>
                  <p><%= config[:description] %></p>
                  <%= form.radio_button :source_type, key, 
                                        class: "platform-radio", 
                                        data: { 
                                          action: "change->data-source-wizard#platformSelected",
                                          config: config.to_json 
                                        } %>
                </div>
              <% end %>
            </div>
            
            <!-- Coming Soon Section -->
            <div class="coming-soon-section">
              <h4>Coming Soon</h4>
              <div class="platforms-grid coming-soon">
                <% data_source_configs.select { |k, v| v[:status] == 'coming_soon' }.each do |key, config| %>
                  <div class="platform-card disabled">
                    <div class="platform-icon <%= key %>">
                      <%= config[:icon] || '🔗' %>
                    </div>
                    <h4><%= config[:name] %></h4>
                    <p><%= config[:description] %></p>
                    <span class="coming-soon-badge">Coming Soon</span>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 2: Configuration -->
        <div class="wizard-step" data-data-source-wizard-target="step2" data-step="2">
          <div class="step-content">
            <h3 class="step-title">Configure Your Connection</h3>
            <p class="step-subtitle">Enter your credentials and set up sync preferences</p>
            
            <div class="configuration-form">
              <!-- Basic Settings -->
              <div class="form-section">
                <h4>Basic Information</h4>
                <div class="form-group">
                  <%= form.label :name, class: "block text-sm font-medium mb-2", style: "color: var(--color-text-secondary);" %>
                  <%= form.text_field :name,
                                      placeholder: "e.g., Production Store",
                                      class: "w-full px-3 py-2 text-sm border-0 focus:ring-2 focus:ring-offset-0",
                                      style: "background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); color: var(--color-text); focus:ring-color: var(--color-primary);",
                                      data: { auto_save_target: "field" } %>
                  <p class="mt-2 text-sm" style="color: var(--color-text-secondary);">Give your integration a memorable name</p>
                </div>

                <div class="form-group">
                  <%= form.label :description, class: "block text-sm font-medium mb-2", style: "color: var(--color-text-secondary);" %>
                  <%= form.text_area :description,
                                     rows: 3,
                                     placeholder: "Optional description...",
                                     class: "w-full px-3 py-2 text-sm border-0 focus:ring-2 focus:ring-offset-0",
                                     style: "background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); color: var(--color-text); focus:ring-color: var(--color-primary);",
                                     data: { auto_save_target: "field" } %>
                </div>
              </div>
              
              <!-- Platform-specific Configuration -->
              <div class="form-section" data-data-source-wizard-target="platformConfig">
                <h4>Authentication</h4>
                <div id="platform-specific-fields">
                  <!-- Fields will be dynamically inserted based on platform selection -->
                </div>
              </div>
              
              <!-- Sync Settings -->
              <div class="form-section">
                <h4>Sync Settings</h4>
                <div class="form-group">
                  <%= form.label :sync_frequency, "Sync Frequency", class: "block text-sm font-medium mb-2", style: "color: var(--color-text-secondary);" %>
                  <%= form.select :sync_frequency,
                                  options_for_select(sync_frequencies.map { |f| [f[:label], f[:value]] }, 'daily'),
                                  {},
                                  class: "w-full px-3 py-2 text-sm border-0 focus:ring-2 focus:ring-offset-0",
                                  style: "background-color: var(--color-surface); border: 1px solid var(--color-card-border); border-radius: var(--radius-lg); color: var(--color-text); focus:ring-color: var(--color-primary);",
                                  data: { auto_save_target: "field" } %>
                  <p class="mt-2 text-sm" style="color: var(--color-text-secondary);">How often should we sync your data?</p>
                </div>
                
                <div class="form-group">
                  <label class="toggle-label">
                    <%= form.check_box :active, class: "toggle-input", checked: true %>
                    <span class="toggle-switch"></span>
                    <span class="toggle-text">Enable automatic syncing</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Data Preview -->
        <div class="wizard-step" data-data-source-wizard-target="step3" data-step="3">
          <div class="step-content">
            <h3 class="step-title">Preview Your Data</h3>
            <p class="step-subtitle">Verify that we can connect and preview your data</p>
            
            <div class="preview-section">
              <!-- Connection Test -->
              <div class="connection-test" data-data-source-wizard-target="connectionTest">
                <div class="test-status">
                  <div class="status-icon loading">
                    <svg class="spin" width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                  </div>
                  <div class="status-message">
                    <h4>Testing Connection...</h4>
                    <p>Verifying your credentials and access permissions</p>
                  </div>
                </div>
                
                <button type="button" 
                        class="btn btn--outline"
                        data-action="click->data-source-wizard#testConnection">
                  Test Connection
                </button>
              </div>
              
              <!-- Data Preview Table -->
              <div class="data-preview" data-data-source-wizard-target="dataPreview">
                <h4>Sample Data</h4>
                <div class="preview-table-container">
                  <p class="preview-placeholder">Test your connection to see a preview of your data</p>
                </div>
              </div>
              
              <!-- Data Mapping -->
              <div class="data-mapping">
                <h4>Field Mapping</h4>
                <p class="form-hint">We'll automatically map your fields to our data model</p>
                <div class="mapping-list" data-data-source-wizard-target="fieldMapping">
                  <!-- Field mappings will be shown here -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4: Final Setup -->
        <div class="wizard-step" data-data-source-wizard-target="step4" data-step="4">
          <div class="step-content">
            <h3 class="step-title">Ready to Launch!</h3>
            <p class="step-subtitle">Review your configuration and start syncing</p>
            
            <div class="review-section">
              <!-- Configuration Summary -->
              <div class="summary-card">
                <h4>Configuration Summary</h4>
                <div class="summary-items" data-data-source-wizard-target="configSummary">
                  <div class="summary-item">
                    <span class="summary-label">Platform</span>
                    <span class="summary-value" data-summary="platform">-</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">Name</span>
                    <span class="summary-value" data-summary="name">-</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">Sync Frequency</span>
                    <span class="summary-value" data-summary="frequency">-</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">Status</span>
                    <span class="summary-value" data-summary="status">Active</span>
                  </div>
                </div>
              </div>
              
              <!-- Next Steps -->
              <div class="next-steps-card">
                <h4>What Happens Next?</h4>
                <div class="steps-timeline">
                  <div class="timeline-item">
                    <div class="timeline-icon">
                      <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div class="timeline-content">
                      <h5>Initial Sync</h5>
                      <p>We'll start syncing your historical data immediately</p>
                    </div>
                  </div>
                  <div class="timeline-item">
                    <div class="timeline-icon">
                      <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    </div>
                    <div class="timeline-content">
                      <h5>Automatic Updates</h5>
                      <p>Your data will sync automatically based on your schedule</p>
                    </div>
                  </div>
                  <div class="timeline-item">
                    <div class="timeline-icon">
                      <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <div class="timeline-content">
                      <h5>View Insights</h5>
                      <p>Access your analytics dashboard to see insights</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Navigation -->
        <div class="wizard-navigation">
          <button type="button" 
                  class="btn btn--outline"
                  data-action="click->data-source-wizard#previousStep"
                  data-data-source-wizard-target="prevButton"
                  disabled>
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            Previous
          </button>
          
          <div class="nav-center">
            <%= link_to "Cancel", data_sources_path, class: "btn btn--ghost" %>
          </div>
          
          <button type="button" 
                  class="btn btn--primary"
                  data-action="click->data-source-wizard#nextStep"
                  data-data-source-wizard-target="nextButton">
            Next
            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
          
          <%= form.submit "Create Integration", 
                          class: "btn btn--primary",
                          data: { data_source_wizard_target: "submitButton" },
                          style: "display: none;" %>
        </div>
        
      <% end %>
    </div>
  </div>

  <!-- Help Section -->
  <div class="help-section">
    <div class="help-card">
      <div class="help-icon">
        <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
        </svg>
      </div>
      <div class="help-content">
        <h3>Integration Guides</h3>
        <p>Step-by-step instructions for each platform</p>
        <%= link_to "View Guides", "#", class: "help-link" %>
      </div>
    </div>
    
    <div class="help-card">
      <div class="help-icon">
        <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-text-secondary);">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <div class="help-content">
        <h3>Need Help?</h3>
        <p>Our support team is here to assist you</p>
        <div class="help-actions">
          <%= link_to "Email Support", "mailto:<EMAIL>", class: "help-link" %>
          <%= link_to "Live Chat", "#", class: "help-link" %>
        </div>
      </div>
    </div>
  </div>

  <!-- Error Display -->
  <% if @data_source.errors.any? %>
    <div class="error-panel">
      <div class="error-icon">
        <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: var(--color-error);">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <div class="error-content">
        <h3>Please fix the following errors:</h3>
        <ul class="error-list">
          <% @data_source.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    </div>
  <% end %>
</div>