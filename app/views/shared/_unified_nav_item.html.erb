<%
  # Determine if this navigation item is active
  current_section = current_page_section
  is_active = current_section == controller || 
              (controller == 'users' && current_section == 'team') || 
              (controller == 'organizations' && current_section == 'organization') ||
              (controller == 'pipeline_dashboard' && current_section == 'pipelines')
%>

<li>
  <%= link_to path,
      class: "unified-nav-item #{is_active ? 'unified-nav-item--active' : ''}",
      style: "display: flex; align-items: center; gap: var(--space-12); padding: var(--space-12); border-radius: var(--radius-lg); text-decoration: none; transition: all var(--duration-normal) var(--ease-standard); #{is_active ? 'background-color: var(--color-primary); color: var(--color-primary-contrast); box-shadow: var(--shadow-md);' : 'color: var(--color-text-secondary);'}" do %>
    
    <!-- Icon with gradient background -->
    <div style="display: flex; align-items: center; justify-content: center; width: var(--space-40); height: var(--space-40); border-radius: var(--radius-lg); background: linear-gradient(135deg, <%= icon_gradient.split(' ').join(', ') %>); box-shadow: var(--shadow-sm); flex-shrink: 0;">
      <svg style="width: var(--space-20); height: var(--space-20); color: white;" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="1.5">
        <path stroke-linecap="round" stroke-linejoin="round" d="<%= icon_path %>" />
      </svg>
    </div>
    
    <!-- Content -->
    <div style="flex: 1; min-width: 0;">
      <div style="font-size: var(--font-size-sm); font-weight: var(--font-weight-semibold); line-height: var(--line-height-tight); margin-bottom: var(--space-2); <%= is_active ? 'color: var(--color-primary-contrast);' : 'color: var(--color-text);' %>">
        <%= title %>
      </div>
      <div style="font-size: var(--font-size-xs); line-height: var(--line-height-tight); <%= is_active ? 'color: rgba(255, 255, 255, 0.8);' : 'color: var(--color-text-secondary);' %>">
        <%= subtitle %>
      </div>
    </div>
    
    <!-- Optional badge or indicator -->
    <% if defined?(badge) && badge.present? %>
      <span style="background-color: var(--color-warning); color: var(--color-warning-contrast); font-size: var(--font-size-xs); font-weight: var(--font-weight-medium); padding: var(--space-2) var(--space-6); border-radius: var(--radius-full);">
        <%= badge %>
      </span>
    <% end %>
    
    <!-- Data sources count badge -->
    <% if title == 'Data Sources' && @data_sources&.any? %>
      <span style="background-color: var(--color-secondary); color: var(--color-text-secondary); font-size: var(--font-size-xs); font-weight: var(--font-weight-medium); padding: var(--space-2) var(--space-6); border-radius: var(--radius-full); border: 1px solid var(--color-card-border);">
        <%= @data_sources.count %>
      </span>
    <% end %>
  <% end %>
</li>
